<template>
  <div class="edit-popup">
    <popup
      ref="popupRef"
      :title="popupTitle"
      :async="true"
      width="550px"
      @confirm="handleSubmit"
      @close="handleClose"
    >
      <el-form class="ls-form" ref="formRef" :rules="rules" :model="formData" label-width="84px">
        <el-form-item label="操作概要" prop="content">
          <el-input
            class="ls-input"
            v-model="formData.content"
            placeholder="请输入操作概要"
            clearable
          />
        </el-form-item>
        <el-form-item label="操作详情" prop="description">
          <el-input
            v-model="formData.description"
            type="textarea"
            :autosize="{ minRows: 4, maxRows: 6 }"
            placeholder="请输入操作详情"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="ファイル説明" prop="imageUpload">
          <upload
            class="mr-3"
            v-model="formData.imgUrlList"
            :data="{ cid: 0 }"
            type="*"
            :show-progress="true"
            listType="picture-card"
            :showFileList="true"
            @success="handleFileSuccess"
          >
            <template #miniImg="{ file }">
              <div>
                <!-- 根据文件类型显示不同的预览 -->
                <div v-if="isImageFile(file.name)" class="file-preview-container">
                  <img class="el-upload-list__item-thumbnail" :src="file.url" alt="error" />
                </div>
                <div v-else class="file-preview-container file-icon-container">
                  <Icon
                    :color="getFileTypeColor(file.name)"
                    :size="42"
                    :name="getFileTypeIcon(file.name)"
                  />
                  <div class="file-name">{{ getFileName(file.name) }}</div>
                </div>

                <span class="el-upload-list__item-actions">
                  <span
                    class="el-upload-list__item-preview"
                    @click="handleFilePreview(file)"
                    :title="isVideoFile(file.name) ? '视频预览' : '文件预览'"
                  >
                    <el-icon>
                      <zoom-in v-if="isImageFile(file.name)" />
                      <video-play v-else-if="isVideoFile(file.name)" />
                      <view v-else />
                    </el-icon>
                  </span>
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleDownload(file)"
                    title="下载文件"
                  >
                    <el-icon><Download /></el-icon>
                  </span>
                  <span
                    class="el-upload-list__item-delete"
                    @click="handleRemove(file)"
                    title="删除文件"
                  >
                    <el-icon><Delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </upload>
        </el-form-item>

        <el-form-item label="技术类型" prop="answer_type">
          <el-select v-model="formData.answer_type" placeholder="请选择技术类型">
            <el-option
              v-for="(item, key) in ANSWER_TYPE"
              :key="key"
              :label="item"
              :value="Number(key)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型" prop="device_type">
          <el-select v-model="formData.device_type" placeholder="请选择设备类型">
            <el-option
              v-for="(item, key) in DEVICE_TYPE"
              :key="key"
              :label="item"
              :value="Number(key)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="formData.sort" :max="9999" />
          <div class="form-tips">数值越大越排前</div>
        </el-form-item>
        <el-form-item label="需要工具" prop="is_need_tools">
          <el-switch v-model="formData.is_need_tools" />
        </el-form-item>
        <el-form-item v-if="formData.is_need_tools" label="工具详情" prop="is_need_detail">
          <el-input
            v-model="formData.is_need_detail"
            type="textarea"
            :autosize="{ minRows: 2, maxRows: 6 }"
            placeholder="请输入工具详情"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
    </popup>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="dialogVisible" title="图片预览" width="60%">
      <img w-full :src="dialogImageUrl" alt="Preview Image" style="max-width: 100%; height: auto;" />
    </el-dialog>

    <!-- 视频预览对话框 -->
    <el-dialog v-model="videoDialogVisible" title="视频预览" width="70%">
      <video
        v-if="videoDialogVisible && dialogVideoUrl"
        :src="dialogVideoUrl"
        controls
        width="100%"
        height="400px"
      >
        您的浏览器不支持视频播放。
      </video>
    </el-dialog>
  </div>
</template>
<script lang="ts" setup>
import { Delete, Download, ZoomIn, VideoPlay, View } from "@element-plus/icons-vue";
import type { FormInstance, UploadFile } from "element-plus";
import { operationAdd, operationEdit } from "@/api/customer-ja";
import { ANSWER_TYPE, DEVICE_TYPE } from "@/utils/constants";
import {
  getFileTypeByExtension,
  isImageFile,
  isVideoFile,
  isAudioFile,
  getFileTypeIcon,
  getFileTypeColor,
} from "@/utils/fileType";
import { downloadFile } from "@/utils/download";

import Popup from "@/components/popup/index.vue";
import feedback from "@/utils/feedback";
const emit = defineEmits(["success", "close"]);
const formRef = shallowRef<FormInstance>();
const popupRef = shallowRef<InstanceType<typeof Popup>>();
const mode = ref("add");

// 文件预览相关
const dialogImageUrl = ref("");
const dialogVisible = ref(false);
const dialogVideoUrl = ref("");
const videoDialogVisible = ref(false);

const popupTitle = computed(() => {
  return mode.value == "edit" ? "编辑操作" : "新增操作";
});

const formData = reactive({
  id: "",
  content: "",
  sort: 0,
  description: "",
  answer_type: 1,
  device_type: 1,
  is_need_tools: false,
  is_need_detail: "",
  imgUrlList: [],
});

const rules = {
  content: [
    {
      required: true,
      message: "请输入操作概要",
      trigger: ["blur"],
    },
  ],
  description: [
    {
      required: false,
      message: "请输入操作详情",
      trigger: ["blur"],
    },
  ],
};

// 文件处理函数
const handleFileSuccess = (file: any, nameUrlList: any) => {
  formData.imgUrlList = nameUrlList;
};

const handleRemove = (file: UploadFile) => {
  formData.imgUrlList = formData.imgUrlList.filter((item: any) => item.name !== file.name);
};

const handleFilePreview = (file: UploadFile) => {
  console.log('预览文件:', file);

  if (isImageFile(file.name)) {
    // 图片预览
    dialogImageUrl.value = file.url!;
    dialogVisible.value = true;
  } else if (isVideoFile(file.name)) {
    // 视频预览
    dialogVideoUrl.value = file.url!;
    videoDialogVisible.value = true;
  } else {
    // 其他文件类型，提示下载
    feedback.msgWarning('该文件类型不支持预览，正在为您下载文件');
    handleDownload(file);
  }
};

const handleDownload = async (file: UploadFile) => {
  console.log('下载文件:', file);

  if (!file.url) {
    feedback.msgError('文件链接无效，无法下载');
    return;
  }

  // 使用统一的下载工具函数
  await downloadFile(file.url, file.name || "download");
};

// 获取文件名（去掉路径）
const getFileName = (filename: string) => {
  if (!filename) return '';
  return filename.split('/').pop() || filename;
};

const handleSubmit = async () => {
  await formRef.value?.validate();
  const params = { ...formData };
  mode.value == "edit" ? await operationEdit(params) : await operationAdd(params);
  popupRef.value?.close();
  feedback.msgSuccess("操作成功");
  emit("success");
};

const handleClose = () => {
  emit("close");
};

const open = (type = "add") => {
  mode.value = type;
  popupRef.value?.open();
};

const setFormData = async (row: Record<any, any>) => {
  const data = row;
  for (const key in formData) {
    if (data[key] != null && data[key] != undefined) {
      //@ts-ignore
      formData[key] = data[key];
    }
  }
};

defineExpose({
  open,
  setFormData,
});
</script>

<style scoped lang="scss">
.file-preview-container {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.file-icon-container {
  flex-direction: column;
  background-color: #f5f7fa;
  border-radius: 6px;
  padding: 8px;

  .file-name {
    font-size: 12px;
    color: #606266;
    text-align: center;
    margin-top: 4px;
    word-break: break-all;
    line-height: 1.2;
    max-height: 24px;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}

.el-upload-list__item-thumbnail {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

// 覆盖 Element Plus 的样式
:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 148px;
  height: 148px;
}

:deep(.el-upload-list__item-actions) {
  .el-upload-list__item-preview,
  .el-upload-list__item-delete {
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      color: var(--el-color-primary);
    }
  }
}
</style>
